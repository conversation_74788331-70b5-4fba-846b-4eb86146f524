// Script de prueba para el endpoint createDeliveryBooking
// Ejecutar con: node test-endpoint-delivery.js

const https = require('https');

// Configuración del endpoint
const ENDPOINT_URL = 'https://us-central1-balle-813e3.cloudfunctions.net/createDeliveryBooking';

// Datos de prueba para crear una entrega agrupada
const testData = {
  "folioEntrega": "ENT-TEST-" + Date.now(),
  "pedidos": [
    {
      "folioPedido": "PED-TEST-001",
      "cliente": {
        "uid": "test_customer_001",
        "nombre": "<PERSON>",
        "telefono": "+52123456789",
        "email": "<EMAIL>"
      },
      "domicilio": {
        "direccion": "Calle Principal 123, Col. Centro",
        "lat": 19.4326,
        "lng": -99.1332,
        "referencias": "Casa azul con portón negro"
      },
      "productos": [
        {
          "id": "PROD-001",
          "nombre": "Producto de Prueba A",
          "cantidad": 2,
          "precio": 150.00
        }
      ],
      "total": 300.00,
      "instrucciones": "Entregar en recepción",
      "observaciones": "Cliente prefiere entrega por la mañana"
    },
    {
      "folioPedido": "PED-TEST-002",
      "cliente": {
        "uid": "test_customer_002",
        "nombre": "María González",
        "telefono": "+52987654321",
        "email": "<EMAIL>"
      },
      "domicilio": {
        "direccion": "Avenida Reforma 456, Col. Roma",
        "lat": 19.4200,
        "lng": -99.1500,
        "referencias": "Edificio moderno, piso 3"
      },
      "productos": [
        {
          "id": "PROD-002",
          "nombre": "Producto de Prueba B",
          "cantidad": 1,
          "precio": 250.00
        }
      ],
      "total": 250.00,
      "instrucciones": "Tocar timbre del apartamento 3B",
      "observaciones": "Disponible después de las 2 PM"
    }
  ],
  "vehiculo": {
    "id": "VEH-TEST-001",
    "tipo": "Camión de Reparto",
    "placas": "TEST-123",
    "modelo": "Ford Transit",
    "marca": "Ford",
    "capacidad": "1000kg"
  },
  "chofer": {
    "uid": "test_driver_001",
    "nombre": "Carlos López",
    "telefono": "+52555123456",
    "pushToken": "test_push_token_123"
  },
  "observacionesGenerales": "Entrega de prueba para validar el sistema"
};

// Función para hacer la petición HTTP
function testEndpoint() {
  const postData = JSON.stringify(testData);
  
  const options = {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Content-Length': Buffer.byteLength(postData),
      // Nota: En producción necesitarás agregar el header de autorización
      // 'Authorization': 'Basic ' + Buffer.from('username:password').toString('base64')
    }
  };

  console.log('🚀 Iniciando prueba del endpoint createDeliveryBooking...');
  console.log('📍 URL:', ENDPOINT_URL);
  console.log('📦 Datos de prueba:', JSON.stringify(testData, null, 2));
  console.log('\n⏳ Enviando petición...\n');

  const req = https.request(ENDPOINT_URL, options, (res) => {
    let data = '';

    res.on('data', (chunk) => {
      data += chunk;
    });

    res.on('end', () => {
      console.log('📊 Código de respuesta:', res.statusCode);
      console.log('📋 Headers de respuesta:', res.headers);
      
      try {
        const response = JSON.parse(data);
        console.log('\n✅ Respuesta del servidor:');
        console.log(JSON.stringify(response, null, 2));
        
        if (res.statusCode === 200 && response.success) {
          console.log('\n🎉 ¡Prueba exitosa!');
          console.log('📝 Folio de entrega creado:', response.folioEntrega);
          console.log('📊 Total de pedidos:', response.totalPedidos);
          console.log('🚛 Conductor asignado:', response.choferAsignado.nombre);
          console.log('🚗 Vehículo asignado:', response.vehiculoAsignado.placas);
        } else {
          console.log('\n❌ Error en la prueba');
          console.log('💬 Mensaje:', response.error || response.message);
        }
      } catch (error) {
        console.log('\n❌ Error al parsear la respuesta JSON:');
        console.log('📄 Respuesta cruda:', data);
        console.log('🐛 Error:', error.message);
      }
    });
  });

  req.on('error', (error) => {
    console.log('\n❌ Error en la petición:');
    console.log('🐛 Error:', error.message);
  });

  req.write(postData);
  req.end();
}

// Función para mostrar información de uso
function showUsage() {
  console.log(`
📚 GUÍA DE USO DEL ENDPOINT createDeliveryBooking

🔗 URL del Endpoint:
${ENDPOINT_URL}

🔑 Autenticación Requerida:
- Header: Authorization: Basic [base64(username:password)]
- Contacta al administrador para obtener las credenciales

📋 Estructura de Datos Requerida:
{
  "folioEntrega": "Identificador único de la entrega",
  "pedidos": [
    {
      "folioPedido": "Folio del pedido individual",
      "cliente": {
        "uid": "ID del cliente",
        "nombre": "Nombre completo",
        "telefono": "Teléfono con código de país",
        "email": "<EMAIL>"
      },
      "domicilio": {
        "direccion": "Dirección completa",
        "lat": 19.4326,
        "lng": -99.1332,
        "referencias": "Referencias adicionales"
      },
      "productos": [
        {
          "id": "ID del producto",
          "nombre": "Nombre del producto",
          "cantidad": 1,
          "precio": 100.00
        }
      ],
      "total": 100.00,
      "instrucciones": "Instrucciones de entrega",
      "observaciones": "Observaciones adicionales"
    }
  ],
  "vehiculo": {
    "id": "ID del vehículo",
    "tipo": "Tipo de vehículo",
    "placas": "Placas del vehículo",
    "modelo": "Modelo",
    "marca": "Marca"
  },
  "chofer": {
    "uid": "ID del conductor",
    "nombre": "Nombre del conductor",
    "telefono": "Teléfono del conductor",
    "pushToken": "Token para notificaciones"
  },
  "observacionesGenerales": "Observaciones generales de la entrega"
}

🎯 Estados del Sistema:
- Booking: NEW → ACCEPTED → REACHED → PENDING → PAID → COMPLETE
- Grupo de Entrega: PENDING → ASSIGNED → IN_PROGRESS → COMPLETED
- Pedido Individual: PENDING → DELIVERED

📱 Notificaciones:
- Se envía notificación push al conductor cuando se asigna la entrega
- Se actualiza el estado del conductor (queue: true)

🗄️ Base de Datos:
- /bookings/{bookingId} - Bookings individuales
- /deliveryGroups/{folioEntrega} - Grupos de entrega
- /users/{uid} - Información de usuarios

⚠️  Nota: Esta es una prueba de ejemplo. En producción necesitarás:
1. Credenciales de autenticación válidas
2. Conductores y vehículos reales en la base de datos
3. Configuración correcta de Firebase
  `);
}

// Ejecutar la prueba
if (process.argv.includes('--help') || process.argv.includes('-h')) {
  showUsage();
} else {
  testEndpoint();
}
